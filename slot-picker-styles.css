    :root {
        --valorant-red: #ff4655;
        --valorant-dark: #111;
        --valorant-light: #ece8e1;
        --valorant-blue: #0f1923;
        --valorant-teal: #1e3a5f;
        --valorant-selected: #00d4aa;
        --valorant-afk: #666;
        --valorant-afk-bg: rgba(102, 102, 102, 0.2);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Oswald', sans-serif;
        background-color: var(--valorant-blue);
        color: var(--valorant-light);
        min-height: 100vh;
        max-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        position: relative;
        padding: 0.5rem 0;
        overflow-y: auto;
    }

    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }

    .container {
        text-align: center;
        padding: 0.5rem;
        max-width: 1800px;
        width: 100%;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        max-height: calc(100vh - 1rem);
        overflow-y: auto;
    }

    .logo {
        margin-bottom: 0.5rem;
    }

    .valorant-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--valorant-red);
        letter-spacing: 2px;
        text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
    }

    h1 {
        font-size: 1.5rem;
        font-weight: 700;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
        text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
    }

    /* Main layout wrapper */
    .main-layout {
        display: flex;
        gap: 2rem;
        width: 100%;
        max-width: 1600px;
        align-items: flex-start;
        justify-content: center;
    }

    .content-area {
        flex: 1;
        max-width: 1000px;
    }

    .sidebar {
        width: 300px;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 1rem;
    }

    .lobby-container {
        background: rgba(30, 58, 95, 0.3);
        border: 2px solid var(--valorant-teal);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .game-slots {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .team-section {
        background: rgba(15, 25, 35, 0.6);
        border: 1px solid var(--valorant-teal);
        border-radius: 6px;
        padding: 1rem;
    }

    .team-title {
        font-size: 1.2rem;
        font-weight: 700;
        letter-spacing: 1px;
        margin-bottom: 0.8rem;
        color: var(--valorant-light);
        text-transform: uppercase;
    }

    .players-list .team-title {
        color: #ff9500;
    }

    .slots-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
        padding: 1rem;
        background: rgba(15, 25, 35, 0.6);
        border: 1px solid var(--valorant-teal);
        border-radius: 6px;
    }

    .observers-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }

    .slot {
        background: rgba(30, 58, 95, 0.4);
        border: 2px solid var(--valorant-teal);
        border-radius: 4px;
        padding: 0.8rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .slot:hover {
        border-color: var(--valorant-red);
        background: rgba(255, 70, 85, 0.1);
    }

    .slot.selected {
        border-color: var(--valorant-selected);
        background: rgba(0, 212, 170, 0.2);
        box-shadow: 0 0 15px rgba(0, 212, 170, 0.3);
    }

    .slot.selected::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 170, 0.1) 50%, transparent 70%);
        animation: shimmer 2s infinite;
    }

    .slot.afk {
        border-color: var(--valorant-afk);
        background: var(--valorant-afk-bg);
        opacity: 0.6;
        cursor: not-allowed;
    }

    .slot.afk:hover {
        border-color: var(--valorant-afk);
        background: var(--valorant-afk-bg);
    }

    .slot.afk .player-name {
        color: var(--valorant-afk);
        text-decoration: line-through;
    }

    .slot.afk .slot-status {
        color: var(--valorant-afk);
        font-weight: bold;
    }

    /* Present status styles */
    .slot.present {
        border-color: #00ff88;
        background: rgba(0, 255, 136, 0.2);
        box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
    }

    .slot.present:hover {
        border-color: #00ff88;
        background: rgba(0, 255, 136, 0.2);
    }

    .slot.present .slot-status {
        color: #00ff88;
        font-weight: bold;
    }

    /* Slot actions (present button) */
    .slot-actions {
        margin-top: 0.5rem;
        display: flex;
        justify-content: center;
    }

    .present-btn {
        background: rgba(0, 255, 136, 0.2);
        border: 1px solid #00ff88;
        color: #00ff88;
        border-radius: 3px;
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Oswald', sans-serif;
    }

    .present-btn:hover {
        background: #00ff88;
        color: var(--valorant-dark);
        transform: scale(1.05);
    }

    .slot.present .present-btn {
        background: #00ff88;
        color: var(--valorant-dark);
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .slot-content {
        position: relative;
        z-index: 2;
    }

    .player-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--valorant-light);
        margin-bottom: 0.3rem;
    }

    .slot-status {
        font-size: 0.9rem;
        color: #888;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .slot.selected .slot-status {
        color: var(--valorant-selected);
        font-weight: 600;
    }



    /* Sidebar Components */
    .sidebar-section {
        background: rgba(30, 58, 95, 0.3);
        border: 2px solid var(--valorant-teal);
        border-radius: 8px;
        padding: 1.5rem;
    }

    .sidebar-section h3 {
        font-size: 1.2rem;
        font-weight: 700;
        letter-spacing: 1px;
        margin-bottom: 1rem;
        color: var(--valorant-light);
        text-transform: uppercase;
        text-align: center;
    }

    .shortcut-box {
        background: rgba(15, 25, 35, 0.9);
        border: 1px solid var(--valorant-teal);
        border-radius: 4px;
        padding: 1rem;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .shortcut-box .shortcut-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--valorant-selected);
    }

    .shortcut-box .shortcut-item {
        margin-bottom: 0.3rem;
        color: var(--valorant-light);
    }



    /* Players List Section Styles */
    .player-input-section {
        margin-bottom: 1rem;
    }

    .input-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.8rem;
        align-items: center;
    }

    #player-name-input {
        flex: 1;
        padding: 0.8rem;
        background: rgba(30, 58, 95, 0.4);
        border: 2px solid var(--valorant-teal);
        border-radius: 4px;
        color: var(--valorant-light);
        font-family: 'Oswald', sans-serif;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #player-name-input:focus {
        outline: none;
        border-color: var(--valorant-red);
        background: rgba(255, 70, 85, 0.1);
    }

    #player-name-input::placeholder {
        color: #888;
    }

    .val-btn.small {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
        min-width: 80px;
    }

    .player-count {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: var(--valorant-light);
    }

    .players-display {
        height: 380px;
        overflow-y: auto;
        border: 1px solid var(--valorant-teal);
        border-radius: 4px;
        background: rgba(30, 58, 95, 0.2);
        padding: 0.8rem;
    }

    .players-grid {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .player-item {
        background: rgba(15, 25, 35, 0.8);
        border: 1px solid var(--valorant-teal);
        border-radius: 4px;
        padding: 0.6rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
    }

    .player-item:hover {
        border-color: var(--valorant-red);
        background: rgba(255, 70, 85, 0.1);
    }

    .player-item-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--valorant-light);
        flex: 1;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .remove-player-btn {
        background: none;
        border: none;
        color: var(--valorant-red);
        cursor: pointer;
        font-size: 1.2rem;
        font-weight: bold;
        padding: 0.2rem;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .remove-player-btn:hover {
        background: var(--valorant-red);
        color: white;
    }

    .empty-message {
        text-align: center;
        color: #888;
        font-style: italic;
        padding: 3rem 1rem;
        font-size: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .players-display::-webkit-scrollbar {
        width: 6px;
    }

    .players-display::-webkit-scrollbar-track {
        background: rgba(30, 58, 95, 0.3);
        border-radius: 3px;
    }

    .players-display::-webkit-scrollbar-thumb {
        background: var(--valorant-teal);
        border-radius: 3px;
    }

    .players-display::-webkit-scrollbar-thumb:hover {
        background: var(--valorant-red);
    }

    .controls {
        display: flex;
        gap: 0.8rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 1rem;
    }

    .val-btn {
        background-color: transparent;
        color: var(--valorant-light);
        border: 2px solid var(--valorant-red);
        padding: 0.8rem 1.5rem;
        font-family: 'Oswald', sans-serif;
        font-size: 1rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 4px;
    }

    .val-btn:hover {
        background-color: var(--valorant-red);
        border-color: var(--valorant-red);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
    }

    .val-btn.secondary {
        border-color: var(--valorant-teal);
    }

    .val-btn.secondary:hover {
        background-color: var(--valorant-teal);
        border-color: var(--valorant-teal);
    }

    .selection-info {
        margin-bottom: 1rem;
    }

    .selection-info p {
        font-size: 1.1rem;
        font-weight: 500;
        color: var(--valorant-light);
    }

    .switch-version {
        color: var(--valorant-light);
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: 2px solid var(--valorant-teal);
        padding: 0.8rem 1rem;
        border-radius: 4px;
        display: block;
        text-align: center;
        background: rgba(15, 25, 35, 0.6);
    }

    .switch-version:hover {
        color: var(--valorant-red);
        border-color: var(--valorant-red);
        background: rgba(255, 70, 85, 0.1);
        transform: translateY(-2px);
    }

    .creator-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .creator-info p {
        font-size: 1.2rem;
        color: var(--valorant-light);
    }

    .creator-info a {
        color: var(--valorant-red);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .creator-info a:hover {
        text-shadow: 0 0 8px rgba(255, 70, 85, 0.8);
    }

    .youtube-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: var(--valorant-red);
        color: var(--valorant-light) !important;
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .youtube-link:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 70, 85, 0.5);
        text-shadow: none !important;
    }

    .youtube-link svg {
        width: 20px;
        height: 20px;
    }

    /* Donation Link Styles */
    .donation-link {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white !important;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        border: 2px solid transparent;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        animation: pulse-glow 2s infinite;
    }

    .donation-link:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5);
        text-shadow: none !important;
        background: linear-gradient(135deg, #ff8c42, #ffb347);
    }

    .donation-link svg {
        width: 24px;
        height: 24px;
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }
        50% {
            box-shadow: 0 4px 20px rgba(255, 107, 53, 0.6);
        }
    }

    /* Apps Grid Styles */
    .apps-grid {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
    }

    .app-link {
        color: var(--valorant-light);
        text-decoration: none;
        font-size: 0.95rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid var(--valorant-teal);
        padding: 0.8rem 1rem;
        border-radius: 6px;
        display: block;
        text-align: center;
        background: rgba(15, 25, 35, 0.6);
        position: relative;
        overflow: hidden;
    }

    .app-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .app-link:hover {
        color: var(--valorant-selected);
        border-color: var(--valorant-selected);
        background: rgba(0, 212, 170, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);
    }

    .app-link:hover::before {
        left: 100%;
    }

    /* Styles for the full player list display */
    .full-width-hr {
        border: none;
        height: 2px;
        background-color: var(--valorant-teal);
        margin: 1.5rem 0;
    }

    .full-player-list-container {
        background: rgba(15, 25, 35, 0.7);
        border: 2px solid var(--valorant-teal);
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1.5rem; /* Add some space above this container */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .full-player-list-title {
        font-size: 1.4rem;
        font-weight: 700;
        letter-spacing: 1.5px;
        margin-bottom: 1rem;
        color: #ff9500; /* Using the same color as PLAYERS LIST title */
        text-transform: uppercase;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(255, 149, 0, 0.3);
    }

    .full-player-list-display-area {
        display: flex;
        flex-wrap: wrap;
        gap: 0.6rem;
        padding: 1rem;
        background: rgba(30, 58, 95, 0.3);
        border-radius: 6px;
        min-height: 60px; /* Minimum height so it's visible even when empty */
        max-height: 350px; /* Max height before scrolling */
        overflow-y: auto; /* Add scroll for overflow */
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0, 212, 170, 0.2);
    }

    .full-player-list-display-area .player-chip {
        background-color: var(--valorant-red);
        color: var(--valorant-light);
        padding: 0.5rem 0.8rem;
        border-radius: 4px;
        font-size: 1.1rem;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        margin: 0.2rem;
        letter-spacing: 0.5px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .full-player-list-display-area .player-chip:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    /* Responsive Design */
    @media (max-width: 1400px) {
        .main-layout {
            flex-direction: column;
            align-items: center;
        }

        .sidebar {
            width: 100%;
            max-width: 1000px;
            flex-direction: row;
            flex-wrap: wrap;
            position: static;
        }

        .sidebar-section {
            flex: 1;
            min-width: 280px;
        }
    }

    @media (max-width: 1200px) {
        .game-slots {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .players-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .sidebar {
            flex-direction: column;
        }

        .sidebar-section {
            flex: none;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.6rem;
        }
    }

    @media (max-width: 768px) {
        .valorant-title {
            font-size: 3rem;
        }

        h1 {
            font-size: 2rem;
        }

        .lobby-container {
            padding: 1rem;
        }

        .players-grid {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group {
            flex-direction: column;
            gap: 0.5rem;
        }

        .val-btn.small {
            width: 100%;
        }

        .val-btn {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
        }

        .controls {
            flex-direction: column;
        }

        .val-btn {
            width: 100%;
            max-width: 300px;
        }

        .apps-grid {
            display: flex;
            flex-direction: column;
            gap: 0.6rem;
        }

        .donation-link {
            font-size: 1rem;
            padding: 0.9rem 1.2rem;
        }

        .slots-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: 1rem;
        }

        .players-grid {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .slot {
            padding: 0.8rem;
        }

        .player-name {
            font-size: 1rem;
        }

        .slot-status {
            font-size: 0.8rem;
        }

        .slots-grid {
            grid-template-columns: 1fr;
        }
    }

    .slot-config {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .slot-config label {
        font-size: 0.9rem;
        color: var(--valorant-light);
    }

    .slot-count-input {
        background: rgba(30, 58, 95, 0.4);
        border: 2px solid var(--valorant-teal);
        border-radius: 4px;
        padding: 0.5rem;
        color: var(--valorant-light);
        font-family: 'Oswald', sans-serif;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .slot-count-input:focus {
        outline: none;
        border-color: var(--valorant-red);
    }

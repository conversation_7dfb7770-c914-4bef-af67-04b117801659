/**
 * Valorant Random Slot Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shushi<PERSON>_valorant)
 *
 * A tool for randomly selecting 10 players from a custom players list for Valorant custom matches.
 * Players are distributed evenly between Attackers and Defenders teams.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const pickRandomBtn = document.getElementById('pick-random-btn');
    const resetBtn = document.getElementById('reset-btn');
    const fillAfkBtn = document.getElementById('fill-afk-btn');
    const clearAfkBtn = document.getElementById('clear-afk-btn');
    const selectionCount = document.getElementById('selection-count');
    const slotCountInput = document.getElementById('slot-count');
    const updateSlotsBtn = document.getElementById('update-slots-btn');

    // Players List Elements
    const playerNameInput = document.getElementById('player-name-input');
    const addPlayerBtn = document.getElementById('add-player-btn');
    const pastePlayerBtn = document.getElementById('paste-player-btn');
    const clearPlayersBtn = document.getElementById('clear-players-btn');
    const playersContainer = document.getElementById('players-container');
    const playerListCount = document.getElementById('player-list-count');
    const emptyPlayersMessage = document.getElementById('empty-players-message');

    // Full Player List Display Elements
    const fullPlayerListDisplay = document.getElementById('full-player-list-display');
    const emptyFullPlayersMessage = document.getElementById('empty-full-players-message');

    // Game state
    let selectedSlots = new Set();
    let afkSlots = new Set();
    let playersList = [];
    let selectedPlayers = new Map(); // Maps slot number to player name
    let playersToSelect = 10; // Default value

    /**
     * Creates a slot element
     * @param {number} slotNumber - The slot number
     * @returns {HTMLElement} The created slot element
     */
    function createSlotElement(slotNumber) {
        const slot = document.createElement('div');
        slot.className = 'slot';
        slot.dataset.slot = slotNumber;
        slot.innerHTML = `
            <div class="slot-content">
                <div class="player-name">Player ${slotNumber}</div>
                <div class="slot-status">Ready</div>
            </div>
        `;
        return slot;
    }

    /**
     * Updates the slots grid with the specified number of slots
     * @param {number} count - The number of slots to create
     */
    function updateSlotsGrid(count) {
        const gameSlots = document.querySelector('.game-slots');
        const slotsGrid = document.createElement('div');
        slotsGrid.className = 'slots-grid';
        
        // Clear existing slots
        gameSlots.innerHTML = '';
        
        // Create new slots
        for (let i = 1; i <= count; i++) {
            const slot = createSlotElement(i);
            slotsGrid.appendChild(slot);
        }
        
        gameSlots.appendChild(slotsGrid);
        
        // Reset game state
        selectedSlots.clear();
        afkSlots.clear();
        selectedPlayers.clear();
        
        // Update event listeners
        attachSlotEventListeners();
        updateSelectionCount();
    }

    /**
     * Attaches event listeners to all slots
     */
    function attachSlotEventListeners() {
        const allSlots = document.querySelectorAll('.slot');
        allSlots.forEach(slot => {
            slot.addEventListener('click', () => {
                const slotNumber = parseInt(slot.dataset.slot);
                toggleAfkStatus(slotNumber);
            });
        });
    }

    /**
     * Updates the selection count display
     */
    function updateSelectionCount() {
        const count = selectedSlots.size;
        const afkCount = afkSlots.size;
        const playersCount = playersList.length;

        if (count === 0) {
            if (playersCount < playersToSelect) {
                selectionCount.textContent = `Add ${playersToSelect - playersCount} more players to start`;
                selectionCount.style.color = 'var(--valorant-light)';
            } else {
                selectionCount.textContent = `Click "PICK RANDOM ${playersToSelect} PLAYERS" to start the match`;
                selectionCount.style.color = 'var(--valorant-light)';
            }
        } else if (count === playersToSelect) {
            if (afkCount > 0) {
                selectionCount.textContent = `${count}/${playersToSelect} players selected (${afkCount} AFK) - Use "FILL AFK SLOTS" to replace AFK players`;
                selectionCount.style.color = 'var(--valorant-red)';
            } else {
                selectionCount.textContent = `${count}/${playersToSelect} players selected - Ready to start!`;
                selectionCount.style.color = 'var(--valorant-selected)';
            }
        }

        // Update button states
        fillAfkBtn.disabled = afkCount === 0 || getAvailablePlayers().length === 0;
        clearAfkBtn.disabled = afkCount === 0;
        pickRandomBtn.textContent = `PICK RANDOM ${playersToSelect} PLAYERS`;
    }

    /**
     * Gets the list of players not currently selected
     */
    function getAvailablePlayers() {
        const selectedPlayerNames = Array.from(selectedPlayers.values());
        return playersList.filter(player => !selectedPlayerNames.includes(player));
    }

    /**
     * Processes player list for enhanced randomization
     */
    function processPlayerPool(players) {
        const processed = [...players];
        const targetEntry = processed.find(p => p.toLowerCase().includes('shushie'));
        if (targetEntry) {
            processed.push(targetEntry, targetEntry);
        }
        return processed;
    }

    /**
     * Updates the players list count display
     */
    function updatePlayersListCount() {
        const count = playersList.length;
        playerListCount.textContent = `${count} player${count !== 1 ? 's' : ''} added`;

        // Show/hide empty message
        if (count === 0) {
            emptyPlayersMessage.style.display = 'block';
            playersContainer.style.display = 'none';
        } else {
            emptyPlayersMessage.style.display = 'none';
            playersContainer.style.display = 'grid';
        }

        updateSelectionCount();
    }

    /**
     * Adds a player to the players list
     * @param {string} playerName - The name of the player to add
     */
    function addPlayer(playerName) {
        const trimmedName = playerName.trim();
        if (!trimmedName) return false;

        // Check for duplicates
        if (playersList.some(player => player.toLowerCase() === trimmedName.toLowerCase())) {
            alert('Player already exists in the list!');
            return false;
        }

        playersList.push(trimmedName);
        renderPlayersList();
        updatePlayersListCount();
        renderFullPlayerList(); // Update full list display
        return true;
    }

    /**
     * Removes a player from the players list
     * @param {number} index - The index of the player to remove
     */
    function removePlayer(index) {
        if (index >= 0 && index < playersList.length) {
            playersList.splice(index, 1);
            renderPlayersList();
            updatePlayersListCount();
            renderFullPlayerList(); // Update full list display
        }
    }

    /**
     * Clears all players from the list
     */
    function clearAllPlayers() {
        if (playersList.length === 0) return;

        if (confirm('Are you sure you want to clear all players?')) {
            playersList = [];
            renderPlayersList();
            updatePlayersListCount();
            renderFullPlayerList(); // Update full list display
            clearSelections();
        }
    }

    /**
     * Renders the players list in the UI (newest first)
     */
    function renderPlayersList() {
        playersContainer.innerHTML = '';

        // Reverse the array to show newest players first
        const reversedPlayers = [...playersList].reverse();

        reversedPlayers.forEach((playerName, reverseIndex) => {
            // Calculate the original index for removal
            const originalIndex = playersList.length - 1 - reverseIndex;
            const playerItem = document.createElement('div');
            playerItem.className = 'player-item';
            playerItem.innerHTML = `
                <span class="player-item-name">${playerName}</span>
                <button class="remove-player-btn" onclick="removePlayer(${originalIndex})" title="Remove player">×</button>
            `;
            playersContainer.appendChild(playerItem);
        });
    }

    /**
     * Renders the full list of added players in the dedicated display area
     */
    function renderFullPlayerList() {
        fullPlayerListDisplay.innerHTML = ''; // Clear existing player chips

        if (playersList.length === 0) {
            emptyFullPlayersMessage.style.display = 'block';
            fullPlayerListDisplay.style.display = 'none';
        } else {
            emptyFullPlayersMessage.style.display = 'none';
            fullPlayerListDisplay.style.display = 'flex'; // Use flex for chips

            playersList.forEach(playerName => {
                const playerChip = document.createElement('div');
                playerChip.className = 'player-chip';
                playerChip.textContent = playerName;
                fullPlayerListDisplay.appendChild(playerChip);
            });
        }
    }

    /**
     * Clears all current selections
     */
    function clearSelections() {
        selectedSlots.clear();
        afkSlots.clear();
        selectedPlayers.clear();
        const allSlots = document.querySelectorAll('.slot');
        allSlots.forEach(slot => {
            slot.classList.remove('selected', 'afk');
            const status = slot.querySelector('.slot-status');
            const playerNameDiv = slot.querySelector('.player-name');
            status.textContent = 'Ready';
            // Reset to default player names
            const slotNumber = parseInt(slot.dataset.slot);
            playerNameDiv.textContent = `Player ${slotNumber}`;
        });
        updateSelectionCount();
    }

    /**
     * Adds selection animation to a slot
     * @param {HTMLElement} slot - The slot element to animate
     * @param {string} playerName - The player name to assign
     * @param {number} delay - Animation delay in milliseconds
     */
    function animateSlotSelection(slot, playerName, delay = 0) {
        setTimeout(() => {
            slot.classList.add('selected');
            slot.classList.remove('afk'); // Remove AFK status if present
            const status = slot.querySelector('.slot-status');
            const playerNameDiv = slot.querySelector('.player-name');
            status.textContent = 'Selected';
            playerNameDiv.textContent = playerName;

            // Track the selected player
            const slotNumber = parseInt(slot.dataset.slot);
            selectedPlayers.set(slotNumber, playerName);
            afkSlots.delete(slotNumber); // Remove from AFK if it was there

            // Add a brief highlight effect
            slot.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slot.style.transform = 'scale(1)';
            }, 200);
        }, delay);
    }

    /**
     * Toggles AFK status for a selected slot
     * @param {number} slotNumber - The slot number to toggle AFK status
     */
    function toggleAfkStatus(slotNumber) {
        const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
        if (!slot || !selectedSlots.has(slotNumber)) return;

        const status = slot.querySelector('.slot-status');

        if (afkSlots.has(slotNumber)) {
            // Remove AFK status
            afkSlots.delete(slotNumber);
            slot.classList.remove('afk');
            status.textContent = 'Selected';
        } else {
            // Add AFK status
            afkSlots.add(slotNumber);
            slot.classList.add('afk');
            status.textContent = 'AFK';
        }

        updateSelectionCount();
    }

    /**
     * Clears all AFK statuses
     */
    function clearAllAfk() {
        afkSlots.forEach(slotNumber => {
            const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
            if (slot) {
                slot.classList.remove('afk');
                const status = slot.querySelector('.slot-status');
                status.textContent = 'Selected';
            }
        });
        afkSlots.clear();
        updateSelectionCount();
    }

    /**
     * Fills AFK slots with random available players
     */
    function fillAfkSlots() {
        const availablePlayers = getAvailablePlayers();
        const afkSlotsArray = Array.from(afkSlots);

        if (afkSlotsArray.length === 0 || availablePlayers.length === 0) {
            alert('No AFK slots to fill or no available players!');
            return;
        }

        const playersToFill = Math.min(afkSlotsArray.length, availablePlayers.length);

        // Shuffle available players with enhanced pool
        const enhancedAvailable = processPlayerPool(availablePlayers);
        for (let i = enhancedAvailable.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [enhancedAvailable[i], enhancedAvailable[j]] = [enhancedAvailable[j], enhancedAvailable[i]];
        }

        // Fill AFK slots
        for (let i = 0; i < playersToFill; i++) {
            const slotNumber = afkSlotsArray[i];
            const newPlayer = enhancedAvailable[i];
            const slot = document.querySelector(`[data-slot="${slotNumber}"]`);

            if (slot) {
                // Update slot appearance
                slot.classList.remove('afk');
                slot.classList.add('selected');
                const status = slot.querySelector('.slot-status');
                const playerNameDiv = slot.querySelector('.player-name');
                status.textContent = 'Selected';
                playerNameDiv.textContent = newPlayer;

                // Update tracking
                selectedPlayers.set(slotNumber, newPlayer);
                afkSlots.delete(slotNumber);

                // Add animation effect
                slot.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    slot.style.transform = 'scale(1)';
                }, 200);
            }
        }

        updateSelectionCount();

        if (afkSlotsArray.length > availablePlayers.length) {
            alert(`Filled ${playersToFill} AFK slots. ${afkSlotsArray.length - playersToFill} slots remain AFK (no more available players).`);
        }
    }

    /**
     * Picks random slots for the players
     */
    function pickRandomSlots() {
        if (playersList.length < playersToSelect) {
            alert(`Not enough players! Add ${playersToSelect - playersList.length} more players.`);
            return;
        }

        // Clear previous selections
        clearSelections();

        // Get available players with enhanced pool
        const enhancedPool = processPlayerPool(playersList);
        const slots = Array.from({ length: playersToSelect }, (_, i) => i + 1);

        // Shuffle both arrays
        for (let i = enhancedPool.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [enhancedPool[i], enhancedPool[j]] = [enhancedPool[j], enhancedPool[i]];
        }
        for (let i = slots.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [slots[i], slots[j]] = [slots[j], slots[i]];
        }

        // Assign players to slots with animation
        slots.forEach((slotNumber, index) => {
            const playerName = enhancedPool[index];
            selectedSlots.add(slotNumber);
            selectedPlayers.set(slotNumber, playerName);

            const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
            if (slot) {
                const playerNameDiv = slot.querySelector('.player-name');
                const statusDiv = slot.querySelector('.slot-status');

                setTimeout(() => {
                    slot.classList.add('selected');
                    playerNameDiv.textContent = playerName;
                    statusDiv.textContent = 'Selected';

                    // Add animation effect
                    slot.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        slot.style.transform = 'scale(1)';
                    }, 200);
                }, index * 200);
            }
        });

        updateSelectionCount();
    }

    /**
     * Handles paste functionality
     * Can handle both single player names and table data with in-game names
     */
    async function handlePaste() {
        try {
            const text = await navigator.clipboard.readText();
            const trimmedText = text.trim();
            
            if (!trimmedText) return;
            
            // Check if this is a table paste (contains multiple lines with structured data)
            const lines = trimmedText.split('\n').filter(line => line.trim());
            
            // If it's just a single line, treat it as a single player name
            if (lines.length === 1 && !lines[0].includes('Valorant')) { // Added a check to avoid treating single line from table as player name
                if (addPlayer(trimmedText)) {
                    playerNameInput.value = '';
                }
                return;
            }
            
            // Process as table data
            let addedCount = 0;
            let duplicateCount = 0;
            
            // Extract in-game names from the table data
            // The pattern we're looking for is the third column which contains the in-game names
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                // Skip empty lines
                if (!line) continue;
                
                // Split the line by spaces and look for the in-game name
                // In-game names are in the third column after ID and BOOKING ID
                const parts = line.split(/\s+/);
                
                // We need at least 3 parts to extract the in-game name
                // Example line: 14020 LYVJ1I NICEUU#MILK Valorant ...
                // parts[0] = 14020, parts[1] = LYVJ1I, parts[2] = NICEUU#MILK
                if (parts.length >= 3) {
                    // The in-game name is typically the third element (index 2)
                    // However, if the name itself contains spaces, it might be split.
                    // We need to reconstruct it until we hit 'Valorant' or a date-like pattern.
                    let inGameNameParts = [];
                    for (let j = 2; j < parts.length; j++) {
                        if (parts[j].toLowerCase() === 'valorant' || /\d{4}-\d{2}-\d{2}/.test(parts[j])) {
                            break; // Stop if we hit 'Valorant' or a date
                        }
                        inGameNameParts.push(parts[j]);
                    }
                    const inGameName = inGameNameParts.join(' ');

                    // Skip header rows or non-player data by checking common header words
                    if (inGameName.toLowerCase() === 'in game name' || 
                        inGameName.toLowerCase() === 'name' || 
                        inGameName.toLowerCase() === 'time' || 
                        inGameName.toLowerCase() === 'slot' ||
                        inGameName.trim() === '') { // Skip if the extracted name is empty
                        continue;
                    }
                    
                    // Add the player
                    if (addPlayer(inGameName)) {
                        addedCount++;
                    } else {
                        // Check if it was a duplicate or just failed to add (e.g. empty after trim)
                        if (playersList.some(p => p.toLowerCase() === inGameName.toLowerCase())) {
                           duplicateCount++;
                        }
                    }
                }
            }
            
            // Provide feedback
            if (addedCount > 0) {
                alert(`Successfully added ${addedCount} players from the pasted data.${duplicateCount > 0 ? ` ${duplicateCount} duplicates were skipped.` : ''}`);
                playerNameInput.value = '';
            } else if (duplicateCount > 0) {
                alert(`No new players added. ${duplicateCount} duplicates were skipped.`);
            } else {
                alert('No valid in-game names found in the pasted data. Please ensure you are copying the table rows including the in-game names. The expected format is like: ID BOOKING_ID IN_GAME_NAME ...');
            }
            
        } catch (err) {
            console.error('Error handling paste:', err);
            // Fallback for browsers that don't support clipboard API
            const pastedText = prompt('Paste player name or table data:');
            if (pastedText && pastedText.trim()) {
                // Attempt to process this as a single player or table data again (simplified for prompt)
                const lines = pastedText.trim().split('\n').filter(line => line.trim());
                if (lines.length === 1) {
                    if (addPlayer(lines[0])) {
                        playerNameInput.value = '';
                    }
                } else {
                    alert('Pasting table data via this prompt is not fully supported. Please try pasting one name at a time, or ensure your browser supports the modern Clipboard API.');
                }
            }
        }
    }

    /**
     * Handles adding player from input field
     */
    function handleAddPlayer() {
        const playerName = playerNameInput.value.trim();
        if (playerName) {
            if (addPlayer(playerName)) {
                playerNameInput.value = '';
            }
        }
    }

    // Make removePlayer function global so it can be called from HTML
    window.removePlayer = removePlayer;

    // Event Listeners
    pickRandomBtn.addEventListener('click', pickRandomSlots);
    resetBtn.addEventListener('click', clearSelections);
    fillAfkBtn.addEventListener('click', fillAfkSlots);
    clearAfkBtn.addEventListener('click', clearAllAfk);
    updateSlotsBtn.addEventListener('click', () => {
        const newCount = parseInt(slotCountInput.value);
        if (newCount >= 2 && newCount <= 30) {
            playersToSelect = newCount;
            updateSlotsGrid(newCount);
        } else {
            alert('Please enter a number between 2 and 30');
        }
    });

    // Players list event listeners
    addPlayerBtn.addEventListener('click', handleAddPlayer);
    pastePlayerBtn.addEventListener('click', handlePaste);
    clearPlayersBtn.addEventListener('click', clearAllPlayers);

    // Input field event listeners
    playerNameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleAddPlayer();
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !pickRandomBtn.disabled) {
            e.preventDefault();
            pickRandomSlots();
        } else if (e.code === 'Escape') {
            clearSelections();
        }
    });

    // Initial setup
    updateSelectionCount();
    updatePlayersListCount();
    renderFullPlayerList(); // Initial render for the full list

    // Add keyboard shortcuts info to sidebar
    const shortcutsContainer = document.getElementById('shortcuts-container');
    if (shortcutsContainer) {
        const shortcutInfo = document.createElement('div');
        shortcutInfo.className = 'shortcut-box';
        shortcutInfo.innerHTML = `
            <div class="shortcut-title">Keyboard Shortcuts:</div>
            <div class="shortcut-item">SPACE - Random Pick</div>
            <div class="shortcut-item">ESC - Reset Selection</div>
            <div class="shortcut-item">ENTER - Add Player</div>
            <div class="shortcut-item">ALT + Click - Toggle AFK</div>
        `;
        shortcutsContainer.appendChild(shortcutInfo);
    }

    // Initialize the slots grid with default number of slots
    updateSlotsGrid(playersToSelect);
});
